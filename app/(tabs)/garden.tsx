import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, FlatList, ActivityIndicator, Alert, Platform, TouchableOpacity } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Plus } from 'lucide-react-native';
import { Colors } from '@/constants/colors';
import { GardenPlantCard } from '@/components/garden/GardenPlantCard';
import { PlantDetailModal } from '@/components/garden/PlantDetailModal';
import { SearchBar } from '@/components/ui/SearchBar';
import { Button } from '@/components/ui/Button';
import { useGarden } from '@/hooks/useGardenStore';
import { GardenPlant } from '@/types/plant';

export default function GardenScreen() {
  const router = useRouter();
  const { plants, isLoading, updatePlant, removePlant } = useGarden();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPlant, setSelectedPlant] = useState<GardenPlant | null>(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState<'identified' | 'diagnosed'>('identified');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Separate plants into identified and diagnosed categories
  const identifiedPlants = plants.filter(plant => !plant.diagnosis);
  const diagnosedPlants = plants.filter(plant => plant.diagnosis);

  // Filter based on active tab and search query
  const getFilteredPlants = () => {
    const plantsToFilter = activeTab === 'identified' ? identifiedPlants : diagnosedPlants;
    return plantsToFilter.filter(
      (plant) =>
        plant.commonName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        plant.scientificName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (plant.nickname && plant.nickname.toLowerCase().includes(searchQuery.toLowerCase()))
    );
  };

  const allFilteredPlants = getFilteredPlants();

  // Pagination logic
  const totalPages = Math.ceil(allFilteredPlants.length / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const paginatedPlants = allFilteredPlants.slice(startIndex, endIndex);

  // Reset to first page when changing tabs or search
  useEffect(() => {
    setCurrentPage(1);
  }, [activeTab, searchQuery, recordsPerPage]);

  const handlePlantPress = (plant: GardenPlant) => {
    setSelectedPlant(plant);
    setModalVisible(true);
  };

  // Update selectedPlant when plants array changes (after save)
  useEffect(() => {
    if (selectedPlant && plants.length > 0) {
      const updatedPlant = plants.find(p => p.id === selectedPlant.id);
      if (updatedPlant) {
        setSelectedPlant(updatedPlant);
      }
    }
  }, [plants, selectedPlant?.id]);

  const handlePlantUpdate = async (plantId: string, updates: { nickname?: string; notes?: string; locationInGarden?: string; healthStatus?: string; isPublic?: boolean }, sourceTab?: 'identified' | 'diagnosed') => {
    try {
      // Convert healthStatus to health_status for database compatibility
      const dbUpdates = {
        nickname: updates.nickname,
        notes: updates.notes,
        location_in_garden: updates.locationInGarden,
        health_status: updates.healthStatus as 'healthy' | 'sick' | 'recovering' | 'critical',
        is_public: updates.isPublic,
      };

      await updatePlant(plantId, dbUpdates);

      // If the plant was edited from the diagnosed tab, ensure we stay on that tab
      if (sourceTab === 'diagnosed') {
        setActiveTab('diagnosed');
      }

      // Show success message
      Alert.alert('Success', 'Plant details saved successfully!', [{ text: 'OK' }]);
    } catch (error) {
      console.error('Error updating plant:', error);
      Alert.alert('Error', 'Failed to save plant details. Please try again.');
    }
  };

  const handlePlantRemove = async (plantId: string) => {

    // Find the plant to get its name for the confirmation dialog
    const plant = plants.find(p => p.id === plantId);
    const plantName = plant?.nickname || plant?.commonName || 'this plant';

    // Use platform-specific confirmation dialog
    let confirmed = false;

    if (Platform.OS === 'web') {
      // Use browser's native confirm dialog for web
      confirmed = window.confirm(`Are you sure you want to remove "${plantName}" from your garden?`);
    } else {
      // Use Alert.alert for native platforms
      return new Promise<void>((resolve) => {
        Alert.alert(
          'Remove Plant',
          `Are you sure you want to remove "${plantName}" from your garden?`,
          [
            { text: 'Cancel', style: 'cancel', onPress: () => resolve() },
            {
              text: 'Remove',
              style: 'destructive',
              onPress: async () => {
                await performRemoval(plantId);
                resolve();
              },
            },
          ]
        );
      });
    }

    if (confirmed) {
      await performRemoval(plantId);
    }
  };

  const performRemoval = async (plantId: string) => {
    try {
      await removePlant(plantId);
      // Close modal after successful removal
      setModalVisible(false);
      setSelectedPlant(null);

      if (Platform.OS === 'web') {
        alert('Plant removed from garden successfully!');
      } else {
        Alert.alert('Success', 'Plant removed from garden successfully!');
      }
    } catch (error) {
      if (Platform.OS === 'web') {
        alert('Failed to remove plant. Please try again.');
      } else {
        Alert.alert('Error', 'Failed to remove plant. Please try again.');
      }
    }
  };

  const renderItem = ({ item }: { item: GardenPlant }) => (
    <GardenPlantCard
      plant={item}
      onPress={() => handlePlantPress(item)}
    />
  );

  const renderEmptyList = () => {
    const categoryName = activeTab === 'identified' ? 'identified plants' : 'diagnosed plants';
    const emptyMessage = activeTab === 'identified'
      ? 'No identified plants in your garden yet'
      : 'No diagnosed plants in your garden yet';
    const actionMessage = activeTab === 'identified'
      ? 'Start by identifying plants and adding them to your garden'
      : 'Diagnose plant problems to see them here';

    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>{emptyMessage}</Text>
        <Text style={styles.emptyText}>{actionMessage}</Text>
        <Button
          title={activeTab === 'identified' ? "Identify a Plant" : "Diagnose a Plant"}
          onPress={() => router.push(activeTab === 'identified' ? '/(tabs)/' : '/(tabs)/diagnose')}
          style={styles.identifyButton}
        />
      </View>
    );
  };

  return (
    <View style={styles.container} testID="garden-screen">
      <Stack.Screen
        options={{
          title: 'My Garden',
        }}
      />

      <SearchBar
        value={searchQuery}
        onChangeText={setSearchQuery}
        placeholder="Search your garden..."
      />

      {/* Category Tabs */}
      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'identified' && styles.activeTab]}
          onPress={() => setActiveTab('identified')}
        >
          <Text style={[styles.tabText, activeTab === 'identified' && styles.activeTabText]}>
            Identified ({identifiedPlants.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'diagnosed' && styles.activeTab]}
          onPress={() => setActiveTab('diagnosed')}
        >
          <Text style={[styles.tabText, activeTab === 'diagnosed' && styles.activeTabText]}>
            Diagnosed ({diagnosedPlants.length})
          </Text>
        </TouchableOpacity>
      </View>

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
        </View>
      ) : (
        <>
          {/* Records per page selector */}
          <View style={styles.paginationControls}>
            <Text style={styles.paginationLabel}>Records per page:</Text>
            <View style={styles.recordsSelector}>
              {[10, 20, 50].map((count) => (
                <TouchableOpacity
                  key={count}
                  style={[
                    styles.recordsOption,
                    recordsPerPage === count && styles.recordsOptionActive
                  ]}
                  onPress={() => setRecordsPerPage(count)}
                >
                  <Text style={[
                    styles.recordsOptionText,
                    recordsPerPage === count && styles.recordsOptionTextActive
                  ]}>
                    {count}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <FlatList
            data={paginatedPlants}
            renderItem={renderItem}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            ListEmptyComponent={renderEmptyList}
            showsVerticalScrollIndicator={false}
            testID="garden-plant-list"
          />

          {/* Pagination controls */}
          {totalPages > 1 && (
            <View style={styles.paginationContainer}>
              <TouchableOpacity
                style={[styles.paginationButton, currentPage === 1 && styles.paginationButtonDisabled]}
                onPress={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                <Text style={[styles.paginationButtonText, currentPage === 1 && styles.paginationButtonTextDisabled]}>
                  Previous
                </Text>
              </TouchableOpacity>

              <View style={styles.pageInfo}>
                <Text style={styles.pageInfoText}>
                  Page {currentPage} of {totalPages}
                </Text>
                <Text style={styles.totalRecordsText}>
                  ({allFilteredPlants.length} total)
                </Text>
              </View>

              <TouchableOpacity
                style={[styles.paginationButton, currentPage === totalPages && styles.paginationButtonDisabled]}
                onPress={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                <Text style={[styles.paginationButtonText, currentPage === totalPages && styles.paginationButtonTextDisabled]}>
                  Next
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </>
      )}



      <PlantDetailModal
        visible={modalVisible}
        plant={selectedPlant}
        onClose={() => {
          setModalVisible(false);
          setSelectedPlant(null);
        }}
        onUpdate={handlePlantUpdate}
        onRemove={handlePlantRemove}
        sourceTab={activeTab}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5', // Grey background as specified
  },
  addButton: {
    marginRight: 8,
  },
  addButtonText: {
    fontSize: 16,
  },
  listContent: {
    paddingTop: 8,
    paddingBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    marginTop: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  emptyText: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
    marginBottom: 24,
  },
  identifyButton: {
    width: 200,
  },

  tabContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.background,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 4,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  activeTab: {
    backgroundColor: Colors.primary,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textLight,
  },
  activeTabText: {
    color: Colors.background,
    fontWeight: '600',
  },
  paginationControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.backgroundLight,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  paginationLabel: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '500',
  },
  recordsSelector: {
    flexDirection: 'row',
    backgroundColor: Colors.background,
    borderRadius: 6,
    padding: 2,
  },
  recordsOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  recordsOptionActive: {
    backgroundColor: Colors.primary,
  },
  recordsOptionText: {
    fontSize: 12,
    color: Colors.textLight,
    fontWeight: '500',
  },
  recordsOptionTextActive: {
    color: Colors.white,
  },
  paginationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.backgroundLight,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  paginationButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: Colors.primary,
    borderRadius: 6,
    minWidth: 80,
    alignItems: 'center',
  },
  paginationButtonDisabled: {
    backgroundColor: Colors.border,
  },
  paginationButtonText: {
    fontSize: 14,
    color: Colors.white,
    fontWeight: '500',
  },
  paginationButtonTextDisabled: {
    color: Colors.textLight,
  },
  pageInfo: {
    alignItems: 'center',
  },
  pageInfoText: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '500',
  },
  totalRecordsText: {
    fontSize: 12,
    color: Colors.textLight,
    marginTop: 2,
  },
});